// Generated by Hive CE
// Do not modify
// Check in to version control

import 'package:hive_ce/hive.dart';
import 'package:fldanplay/hive/hive_adapters.dart';

extension HiveRegistrar on HiveInterface {
  void registerAdapters() {
    registerAdapter(HistoriesTypeAdapter());
    registerAdapter(HistoryAdapter());
    registerAdapter(StorageAdapter());
    registerAdapter(StorageTypeAdapter());
  }
}

extension IsolatedHiveRegistrar on IsolatedHiveInterface {
  void registerAdapters() {
    registerAdapter(HistoriesTypeAdapter());
    registerAdapter(HistoryAdapter());
    registerAdapter(StorageAdapter());
    registerAdapter(StorageTypeAdapter());
  }
}
