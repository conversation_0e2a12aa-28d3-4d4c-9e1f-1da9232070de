name: Release builds

on:
  push:
    tags:
      - "v*"
  workflow_dispatch:

env:
  JAVA_VERSION: "21"

jobs:
  android-linux:
    name: Build Android and Linux
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install Linux desktop deps
        run: |
          sudo apt-get update
          sudo apt-get install -y clang cmake ninja-build pkg-config libgtk-3-dev liblzma-dev rpm patchelf libmpv-dev mpv

      - name: Set up Java ${{ env.JAVA_VERSION }}
        uses: actions/setup-java@v4
        with:
          distribution: jetbrains
          java-version: ${{ env.JAVA_VERSION }}

      - name: Set up Flutter
        uses: subosito/flutter-action@v2
        with:
          channel: stable
          flutter-version-file: pubspec.yaml

      - name: Download Android keystore
        id: android_keystore
        uses: timheuer/base64-to-file@v1.2
        with:
          fileName: keystore.jks
          encodedString: ${{ secrets.ANDROID_KEYSTORE_BASE64 }}

      - name: Create key.properties
        env: 
          ANDROID_KEYSTORE_PASSWORD: ${{ secrets.ANDROID_KEYSTORE_PASSWORD }}
          ANDROID_KEY_PASSWORD: ${{ secrets.ANDROID_KEY_PASSWORD }}
          ANDROID_KEY_ALIAS: ${{ secrets.ANDROID_KEY_ALIAS }}
        run: |
          echo "storeFile=${{ steps.android_keystore.outputs.filePath }}" > android/key.properties
          echo "storePassword=${{ env.ANDROID_KEYSTORE_PASSWORD }}" >> android/key.properties
          echo "keyPassword=${{ env.ANDROID_KEY_PASSWORD }}" >> android/key.properties
          echo "keyAlias=${{ env.ANDROID_KEY_ALIAS }}" >> android/key.properties

      - name: Build APKs
        run: |
          flutter build apk --release --split-per-abi

      - name: Collect APK artifacts
        run: |
          mkdir -p dist
          TAG="${GITHUB_REF_NAME}"
          cp build/app/outputs/flutter-apk/app-arm64-v8a-release.apk dist/fldanplay-${TAG}-android-arm64-v8a.apk
          cp build/app/outputs/flutter-apk/app-x86_64-release.apk   dist/fldanplay-${TAG}-android-x86_64.apk
          cp build/app/outputs/flutter-apk/app-armeabi-v7a-release.apk dist/fldanplay-${TAG}-android-armeabi-v7a.apk

      - name: Upload APK artifacts
        uses: actions/upload-artifact@v4
        with:
          name: android-apk
          path: dist/*.apk

      - name: Enable Linux desktop
        run: flutter config --enable-linux-desktop

      - name: Build Flutter for Linux
        run: flutter build linux

      - name: Package linux build output
        run: |
          # Tarball package
          TAG="${GITHUB_REF_NAME}"
          tar -zcvf fldanplay-${TAG}-linux-amd64.tar.gz -C build/linux/x64/release/bundle .

      - name: Upload Linux artifacts
        uses: actions/upload-artifact@v4
        with:
          name: linux
          path: |
            fldanplay-*.tar.gz

  windows:
    name: Build Windows
    runs-on: windows-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Flutter (${{ env.FLUTTER_CHANNEL }})
        uses: subosito/flutter-action@v2
        with:
          channel: ${{ env.FLUTTER_CHANNEL }}
          cache: true

      - name: Enable Windows desktop
        shell: pwsh
        run: flutter config --enable-windows-desktop

      - name: Pub get
        shell: pwsh
        run: flutter pub get

      - name: Build Windows
        shell: pwsh
        run: flutter build windows --release

      - name: Zip Windows build
        shell: pwsh
        run: |
          $ErrorActionPreference = 'Stop'
          New-Item -ItemType Directory -Force -Path dist | Out-Null
          $tag = $env:GITHUB_REF_NAME
          $src = Join-Path build windows\x64\runner\Release
          $zip = Join-Path dist ("fldanplay-$tag-windows-x64.zip")
          Compress-Archive -Path "$src\*" -DestinationPath $zip -Force

      # - name: Prepare Windows signing cert (if provided)
      #   if: ${{ secrets.WINDOWS_PFX_BASE64 != '' }}
      #   shell: pwsh
      #   run: |
      #     $bytes = [System.Convert]::FromBase64String($env:WINDOWS_PFX_BASE64)
      #     [System.IO.File]::WriteAllBytes("windows\\app_signing.pfx", $bytes)
      #   env:
      #     WINDOWS_PFX_BASE64: ${{ secrets.WINDOWS_PFX_BASE64 }}

      # - name: Build MSIX via msix package
      #   shell: pwsh
      #   env:
      #     WINDOWS_PFX_PASSWORD: ${{ secrets.WINDOWS_PFX_PASSWORD }}
      #   run: |
      #     flutter pub global activate msix
      #     dart run msix:build
      #     # Collect any produced .msix
      #     New-Item -ItemType Directory -Force -Path dist | Out-Null
      #     Get-ChildItem -Path build -Recurse -Filter *.msix | ForEach-Object {
      #       Copy-Item $_.FullName (Join-Path dist ("app-$env:GITHUB_REF_NAME-windows-x64.msix")) -Force
      #     }

      - name: Upload Windows artifacts
        uses: actions/upload-artifact@v4
        with:
          name: windows
          path: |
            dist/*.zip
          if-no-files-found: warn

  # macos:
  #   name: Build macOS
  #   runs-on: macos-latest
  #   steps:
  #     - uses: actions/checkout@v4
  #     - uses: subosito/flutter-action@v2
  #       with:
  #         channel: ${{ env.FLUTTER_CHANNEL }}
  #         cache: true
  #     - run: flutter config --enable-macos-desktop
  #     - run: flutter pub get
  #     - run: flutter build macos --release
  #     - name: Upload macOS artifact
  #       uses: actions/upload-artifact@v4
  #       with:
  #         name: macos
  #         path: build/macos/Build/Products/Release/*.app

  # ios:
  #   name: Build iOS
  #   runs-on: macos-latest
  #   steps:
  #     - uses: actions/checkout@v4
  #     - uses: subosito/flutter-action@v2
  #       with:
  #         channel: ${{ env.FLUTTER_CHANNEL }}
  #         cache: true
  #     - run: flutter pub get
  #     - name: Build iOS
  #       run: |
  #         flutter build ipa --release
  #     - name: Upload iOS artifact
  #       uses: actions/upload-artifact@v4
  #       with:
  #         name: ios
  #         path: build/ios/ipa/*.ipa

  release:
    name: Create draft GitHub Release
    runs-on: ubuntu-latest
    needs: [android-linux, windows]
    permissions: write-all
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          pattern: "*"
          merge-multiple: true

      - name: Create draft release and upload assets
        uses: softprops/action-gh-release@v2
        with:
          draft: true
          name: ${{ github.ref_name }}
          tag_name: ${{ github.ref_name }}
          files: |
            **/*.apk
            **/*.zip
            **/*.tar.gz

